<template>
  <div class="preview-window">
    <div class="video-container">
      <video
        ref="videoElement"
        class="video-player"
        :src="currentVideoSrc"
        @loadedmetadata="onVideoLoaded"
        @timeupdate="onTimeUpdate"
        @ended="onVideoEnded"
        preload="metadata"
      />
      <div v-if="!currentVideoSrc" class="placeholder">
        <div class="placeholder-content">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 5v14l11-7z"/>
          </svg>
          <p>预览窗口</p>
          <p class="hint">拖拽视频文件到时间轴开始编辑</p>
        </div>
      </div>
    </div>
    
    <!-- 时间显示 -->
    <div class="time-display">
      {{ formatTime(videoStore.currentTime) }} / {{ formatTime(videoStore.totalDuration) }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useVideoStore } from '../stores/counter'

const videoStore = useVideoStore()
const videoElement = ref<HTMLVideoElement>()

const currentVideoSrc = computed(() => {
  return videoStore.currentClip?.url || null
})

const currentClipTime = computed(() => {
  if (!videoStore.currentClip) return 0
  return videoStore.currentTime - videoStore.currentClip.timelinePosition
})

// 监听播放状态变化
watch(() => videoStore.isPlaying, (isPlaying) => {
  if (!videoElement.value) return
  
  if (isPlaying) {
    videoElement.value.play()
  } else {
    videoElement.value.pause()
  }
})

// 监听当前时间变化
watch(() => videoStore.currentTime, (newTime) => {
  if (!videoElement.value || !videoStore.currentClip) return
  
  const clipTime = newTime - videoStore.currentClip.timelinePosition
  if (Math.abs(videoElement.value.currentTime - clipTime) > 0.1) {
    videoElement.value.currentTime = clipTime
  }
})

// 监听当前片段变化
watch(() => videoStore.currentClip, (newClip) => {
  if (!videoElement.value) return
  
  if (newClip) {
    const clipTime = videoStore.currentTime - newClip.timelinePosition
    videoElement.value.currentTime = Math.max(0, clipTime)
  }
})

function onVideoLoaded() {
  if (!videoElement.value || !videoStore.currentClip) return
  
  const clipTime = videoStore.currentTime - videoStore.currentClip.timelinePosition
  videoElement.value.currentTime = Math.max(0, clipTime)
}

function onTimeUpdate() {
  if (!videoElement.value || !videoStore.currentClip) return
  
  const newTime = videoStore.currentClip.timelinePosition + videoElement.value.currentTime
  if (newTime <= videoStore.currentClip.timelinePosition + videoStore.currentClip.duration) {
    videoStore.setCurrentTime(newTime)
  }
}

function onVideoEnded() {
  if (!videoStore.currentClip) return
  
  // 视频片段播放结束，跳转到下一个片段或停止
  const nextTime = videoStore.currentClip.timelinePosition + videoStore.currentClip.duration
  if (nextTime < videoStore.totalDuration) {
    videoStore.setCurrentTime(nextTime)
  } else {
    videoStore.stop()
  }
}

function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

onMounted(() => {
  // 初始化视频元素
  if (videoElement.value) {
    videoElement.value.volume = 1
  }
})
</script>

<style scoped>
.preview-window {
  width: 100%;
  height: 100%;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  border: 2px solid #333;
  box-sizing: border-box;
}

.video-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.video-player {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  display: block;
  /* 确保视频不会超出容器 */
  box-sizing: border-box;
}

.placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #111;
}

.placeholder-content {
  text-align: center;
  color: #666;
}

.placeholder-content svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.placeholder-content p {
  margin: 8px 0;
}

.hint {
  font-size: 14px;
  opacity: 0.7;
}

.time-display {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  font-family: monospace;
}
</style>
