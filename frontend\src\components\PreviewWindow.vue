<template>
  <div class="preview-window">
    <div class="video-container">
      <video
        ref="videoElement"
        class="video-player"
        :src="currentVideoSrc"
        @loadedmetadata="onVideoLoaded"
        @timeupdate="onTimeUpdate"
        @ended="onVideoEnded"
        preload="metadata"
      />
      <!-- 空白区域显示（播放时显示黑屏，非播放时显示提示） -->
      <div v-if="!currentVideoSrc" class="placeholder" :class="{ 'playing-blank': isPlayingBlankArea }">
        <div v-if="!isPlayingBlankArea" class="placeholder-content">
          <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 5v14l11-7z"/>
          </svg>
          <p>预览窗口</p>
          <p class="hint">拖拽视频文件到时间轴开始编辑</p>
        </div>
        <div v-else class="blank-area-indicator">
          <div class="time-indicator">{{ formatTime(videoStore.currentTime) }}</div>
        </div>
      </div>
    </div>
    
    <!-- 时间显示 -->
    <div class="time-display">
      {{ formatTime(videoStore.currentTime) }} / {{ formatTime(videoStore.totalDuration) }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useVideoStore } from '../stores/counter'

const videoStore = useVideoStore()
const videoElement = ref<HTMLVideoElement>()

const currentVideoSrc = computed(() => {
  return videoStore.currentClip?.url || undefined
})

const currentClipTime = computed(() => {
  if (!videoStore.currentClip) return 0
  return videoStore.currentTime - videoStore.currentClip.timelinePosition
})

// 判断是否在播放空白区域
const isPlayingBlankArea = computed(() => {
  return videoStore.isPlaying && !videoStore.currentClip
})

// 监听播放状态变化
watch(() => videoStore.isPlaying, (isPlaying) => {
  if (!videoElement.value || !videoStore.currentClip) return

  if (isPlaying) {
    videoElement.value.play()
  } else {
    videoElement.value.pause()
  }
})

// 监听当前时间变化，同步视频播放位置
watch(() => videoStore.currentTime, (newTime) => {
  if (!videoElement.value || !videoStore.currentClip) return

  const clipTime = newTime - videoStore.currentClip.timelinePosition
  // 确保视频时间在片段范围内
  if (clipTime >= 0 && clipTime <= videoStore.currentClip.duration) {
    if (Math.abs(videoElement.value.currentTime - clipTime) > 0.2) {
      videoElement.value.currentTime = clipTime
    }
  }
})

// 监听当前片段变化，切换视频源
watch(() => videoStore.currentClip, (newClip, oldClip) => {
  if (!videoElement.value) return

  if (newClip && newClip !== oldClip) {
    // 切换到新的视频片段
    const clipTime = videoStore.currentTime - newClip.timelinePosition
    videoElement.value.currentTime = Math.max(0, Math.min(clipTime, newClip.duration))

    // 如果正在播放，确保新视频也开始播放
    if (videoStore.isPlaying) {
      videoElement.value.play()
    }
  } else if (!newClip && videoStore.isPlaying) {
    // 进入空白区域，暂停视频但保持播放状态
    videoElement.value.pause()
  }
})

function onVideoLoaded() {
  if (!videoElement.value || !videoStore.currentClip) return
  
  const clipTime = videoStore.currentTime - videoStore.currentClip.timelinePosition
  videoElement.value.currentTime = Math.max(0, clipTime)
}

function onTimeUpdate() {
  // 不再由视频元素驱动全局时间，而是由全局时间控制器驱动
  // 这里只做同步检查，确保视频播放位置正确
  if (!videoElement.value || !videoStore.currentClip) return

  const expectedClipTime = videoStore.currentTime - videoStore.currentClip.timelinePosition
  const actualClipTime = videoElement.value.currentTime

  // 如果时间差异太大，进行同步
  if (Math.abs(expectedClipTime - actualClipTime) > 0.5) {
    videoElement.value.currentTime = Math.max(0, Math.min(expectedClipTime, videoStore.currentClip.duration))
  }
}

function onVideoEnded() {
  // 视频片段播放结束，但不停止全局播放
  // 全局时间控制器会继续推进时间，可能进入空白区域或下一个片段
  console.log('Video clip ended, continuing timeline playback')
}

function formatTime(seconds: number): string {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

onMounted(() => {
  // 初始化视频元素
  if (videoElement.value) {
    videoElement.value.volume = 1
  }
})
</script>

<style scoped>
.preview-window {
  width: 100%;
  height: 100%;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  border: 2px solid #333;
  box-sizing: border-box;
}

.video-container {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.video-player {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
  object-fit: contain;
  display: block;
  /* 确保视频不会超出容器 */
  box-sizing: border-box;
}

.placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #111;
  transition: background-color 0.3s ease;
}

.placeholder.playing-blank {
  background-color: #000;
}

.placeholder-content {
  text-align: center;
  color: #666;
}

.placeholder-content svg {
  margin-bottom: 16px;
  opacity: 0.5;
}

.placeholder-content p {
  margin: 8px 0;
}

.hint {
  font-size: 14px;
  opacity: 0.7;
}

.blank-area-indicator {
  text-align: center;
  color: #666;
}

.time-indicator {
  font-size: 18px;
  font-family: monospace;
  color: #888;
  background-color: rgba(0, 0, 0, 0.8);
  padding: 8px 16px;
  border-radius: 4px;
  border: 1px solid #333;
}

.time-display {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  font-family: monospace;
}
</style>
