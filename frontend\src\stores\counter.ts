import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

export interface VideoClip {
  id: string
  file: File
  url: string
  duration: number
  startTime: number
  endTime: number
  timelinePosition: number
  name: string
}

export const useVideoStore = defineStore('video', () => {
  const clips = ref<VideoClip[]>([])
  const currentTime = ref(0)
  const isPlaying = ref(false)
  const timelineDuration = ref(60) // 默认60秒时间轴
  const currentClip = ref<VideoClip | null>(null)
  const playbackRate = ref(1) // 播放速度

  // 全局时间控制器
  let timeUpdateInterval: number | null = null

  const totalDuration = computed(() => {
    if (clips.value.length === 0) return timelineDuration.value
    const maxEndTime = Math.max(...clips.value.map(clip => clip.timelinePosition + clip.duration))
    return Math.max(maxEndTime, timelineDuration.value)
  })

  function addClip(clip: VideoClip) {
    clips.value.push(clip)
  }

  function removeClip(clipId: string) {
    const index = clips.value.findIndex(clip => clip.id === clipId)
    if (index > -1) {
      clips.value.splice(index, 1)
    }
  }

  function updateClipPosition(clipId: string, newPosition: number) {
    const clip = clips.value.find(c => c.id === clipId)
    if (clip) {
      clip.timelinePosition = newPosition
    }
  }

  function getClipAtTime(time: number): VideoClip | null {
    return clips.value.find(clip =>
      time >= clip.timelinePosition &&
      time < clip.timelinePosition + clip.duration
    ) || null
  }

  function setCurrentTime(time: number) {
    currentTime.value = time
    currentClip.value = getClipAtTime(time)
  }

  function startTimeUpdate() {
    if (timeUpdateInterval) return

    timeUpdateInterval = setInterval(() => {
      if (isPlaying.value) {
        const newTime = currentTime.value + (0.1 * playbackRate.value) // 每100ms更新一次
        if (newTime >= totalDuration.value) {
          stop()
        } else {
          setCurrentTime(newTime)
        }
      }
    }, 100) // 100ms间隔，确保流畅播放
  }

  function stopTimeUpdate() {
    if (timeUpdateInterval) {
      clearInterval(timeUpdateInterval)
      timeUpdateInterval = null
    }
  }

  function play() {
    isPlaying.value = true
    startTimeUpdate()
  }

  function pause() {
    isPlaying.value = false
    stopTimeUpdate()
  }

  function stop() {
    isPlaying.value = false
    currentTime.value = 0
    stopTimeUpdate()
  }

  function setPlaybackRate(rate: number) {
    playbackRate.value = rate
  }

  return {
    clips,
    currentTime,
    isPlaying,
    timelineDuration,
    currentClip,
    totalDuration,
    playbackRate,
    addClip,
    removeClip,
    updateClipPosition,
    getClipAtTime,
    setCurrentTime,
    play,
    pause,
    stop,
    setPlaybackRate,
    startTimeUpdate,
    stopTimeUpdate
  }
})
