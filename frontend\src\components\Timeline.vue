<template>
  <div class="timeline">
    <!-- 工具栏 -->
    <div v-if="clips.length > 0" class="timeline-toolbar">
      <div class="toolbar-section">
        <span class="toolbar-label">片段管理:</span>
        <button
          class="toolbar-btn"
          @click="autoArrange"
          title="自动排列片段，消除重叠"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M3,3H21V5H3V3M3,7H15V9H3V7M3,11H21V13H3V11M3,15H15V17H3V15M3,19H21V21H3V19Z" />
          </svg>
          自动排列
        </button>
        <span v-if="overlappingCount > 0" class="overlap-warning">
          ⚠️ {{ overlappingCount }} 个重叠
        </span>
      </div>
    </div>

    <div
      class="timeline-container"
      ref="timelineContainer"
      @dragover="handleDragOver"
      @drop="handleDrop"
    >
      <!-- 拖拽提示 -->
      <div v-if="clips.length === 0" class="drop-zone">
        <div class="drop-hint">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
          </svg>
          <p>拖拽视频文件到此处</p>
          <p class="hint">支持 MP4, WebM, AVI 等格式</p>
        </div>
      </div>
      
      <!-- 视频片段 -->
      <VideoClip
        v-for="clip in clips"
        :key="clip.id"
        :clip="clip"
        :timeline-width="timelineWidth"
        :total-duration="videoStore.totalDuration"
        @update-position="handleClipPositionUpdate"
        @remove="handleClipRemove"
      />
      
      <!-- 时间轴背景网格 -->
      <div class="timeline-grid">
        <div
          v-for="i in gridLines"
          :key="i"
          class="grid-line"
          :style="{ left: (i / videoStore.totalDuration) * timelineWidth + 'px' }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useVideoStore, type VideoClip as VideoClipType } from '../stores/counter'
import VideoClip from './VideoClip.vue'

const videoStore = useVideoStore()
const timelineContainer = ref<HTMLElement>()
const timelineWidth = ref(800)

const clips = computed(() => videoStore.clips)

// 计算重叠片段数量
const overlappingCount = computed(() => {
  return videoStore.getOverlappingClips().length
})

// 网格线
const gridLines = computed(() => {
  const lines = []
  const interval = 5 // 每5秒一条网格线
  for (let i = 0; i <= videoStore.totalDuration; i += interval) {
    lines.push(i)
  }
  return lines
})

function updateTimelineWidth() {
  if (timelineContainer.value) {
    timelineWidth.value = timelineContainer.value.clientWidth
  }
}

function handleDragOver(event: DragEvent) {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'copy'
}

async function handleDrop(event: DragEvent) {
  event.preventDefault()
  
  const files = Array.from(event.dataTransfer?.files || [])
  const videoFiles = files.filter(file => file.type.startsWith('video/'))
  
  if (videoFiles.length === 0) {
    alert('请拖拽视频文件')
    return
  }
  
  // 计算拖拽位置对应的时间
  const rect = timelineContainer.value!.getBoundingClientRect()
  const dropX = event.clientX - rect.left
  const dropTime = (dropX / timelineWidth.value) * videoStore.totalDuration
  
  for (const file of videoFiles) {
    await createVideoClip(file, dropTime)
  }
}

async function createVideoClip(file: File, startTime: number): Promise<void> {
  return new Promise((resolve) => {
    const url = URL.createObjectURL(file)
    const video = document.createElement('video')
    
    video.onloadedmetadata = () => {
      const clip: VideoClipType = {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        file,
        url,
        duration: video.duration,
        startTime: 0,
        endTime: video.duration,
        timelinePosition: Math.max(0, startTime),
        name: file.name
      }
      
      videoStore.addClip(clip)
      resolve()
    }
    
    video.src = url
  })
}

function handleClipPositionUpdate(clipId: string, newPosition: number) {
  videoStore.updateClipPosition(clipId, newPosition)
}

function handleClipRemove(clipId: string) {
  videoStore.removeClip(clipId)
}

function autoArrange() {
  videoStore.autoArrangeClips()
}

onMounted(() => {
  updateTimelineWidth()
  window.addEventListener('resize', updateTimelineWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateTimelineWidth)
})
</script>

<style scoped>
.timeline {
  flex: 1;
  background-color: #2a2a2a;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.timeline-toolbar {
  background-color: #333;
  padding: 8px 12px;
  border-bottom: 1px solid #444;
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 40px;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-label {
  font-size: 12px;
  color: #ccc;
  font-weight: 500;
}

.toolbar-btn {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: background-color 0.2s;
}

.toolbar-btn:hover {
  background-color: #357abd;
}

.toolbar-btn svg {
  width: 14px;
  height: 14px;
}

.overlap-warning {
  color: #ff6b6b;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.timeline-container {
  width: 100%;
  flex: 1;
  position: relative;
  min-height: 120px;
}

.drop-zone {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #555;
  border-radius: 4px;
  margin: 10px;
}

.drop-hint {
  text-align: center;
  color: #888;
}

.drop-hint svg {
  margin-bottom: 12px;
  opacity: 0.6;
}

.drop-hint p {
  margin: 4px 0;
}

.hint {
  font-size: 14px;
  opacity: 0.7;
}

.timeline-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.grid-line {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 1px;
  background-color: #444;
  opacity: 0.5;
}
</style>
